Error: 'dataset'

Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/run_breast_reconstruction_corrected.py", line 304, in main
    output_dict, profiling_info = run_reconstruction(images, model, device)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/run_breast_reconstruction_corrected.py", line 97, in run_reconstruction
    output_dict, profiling_info = inference(
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/dust3r/inference_multiview.py", line 82, in inference
    res = loss_of_one_batch(
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/dust3r/inference_multiview.py", line 56, in loss_of_one_batch
    preds = model(views, profiling=profiling)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 328, in forward
    print(f"view_idx: {view_idx}\n, view name: {view_name(view)}\n, image content: {view['img']}\n, true_shape: {view['true_shape']}")
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/dust3r/datasets/base/base_stereo_view_dataset.py", line 237, in view_name
    db = sel(view["dataset"])
KeyError: 'dataset'
