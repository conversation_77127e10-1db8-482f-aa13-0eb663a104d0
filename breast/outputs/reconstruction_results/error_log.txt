错误: equal(): argument 'input' (position 1) must be Tensor, not numpy.ndarray

Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/final_run.py", line 92, in main
    output = model(batch)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 335, in forward
    different_resolution_across_views = not all(torch.equal(shapes[0], shape) for shape in shapes)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 335, in <genexpr>
    different_resolution_across_views = not all(torch.equal(shapes[0], shape) for shape in shapes)
TypeError: equal(): argument 'input' (position 1) must be Tensor, not numpy.ndarray
