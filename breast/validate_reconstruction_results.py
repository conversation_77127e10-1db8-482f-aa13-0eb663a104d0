#!/usr/bin/env python3
"""
Validation script for Fast3R breast reconstruction results
Analyzes the quality and completeness of the 3D reconstruction
"""

import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime

def validate_reconstruction_results(results_dir):
    """Validate and analyze reconstruction results"""
    results_path = Path(results_dir)
    
    print("🔍 Fast3R Breast Reconstruction Results Validation")
    print("=" * 60)
    
    # Check if all expected files exist
    expected_files = [
        "breast_reconstruction.ply",
        "breast_reconstruction.xyz", 
        "camera_poses.npy",
        "camera_poses.txt",
        "combined_points.npy",
        "reconstruction_summary.txt"
    ]
    
    missing_files = []
    for file in expected_files:
        if not (results_path / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All expected output files present")
    
    # Load and analyze point cloud data
    print("\n📊 Point Cloud Analysis:")
    combined_points = np.load(results_path / "combined_points.npy")
    print(f"   Total points: {len(combined_points):,}")
    print(f"   Data type: {combined_points.dtype}")
    print(f"   Shape: {combined_points.shape}")
    
    # Check for valid points (no NaN or infinite values)
    valid_mask = np.isfinite(combined_points).all(axis=1)
    valid_points = combined_points[valid_mask]
    print(f"   Valid points: {len(valid_points):,} ({len(valid_points)/len(combined_points)*100:.1f}%)")
    
    if len(valid_points) > 0:
        print(f"   X range: [{valid_points[:, 0].min():.4f}, {valid_points[:, 0].max():.4f}]")
        print(f"   Y range: [{valid_points[:, 1].min():.4f}, {valid_points[:, 1].max():.4f}]")
        print(f"   Z range: [{valid_points[:, 2].min():.4f}, {valid_points[:, 2].max():.4f}]")
        
        # Calculate point cloud statistics
        centroid = valid_points.mean(axis=0)
        print(f"   Centroid: [{centroid[0]:.4f}, {centroid[1]:.4f}, {centroid[2]:.4f}]")
        
        # Calculate bounding box dimensions
        bbox_size = valid_points.max(axis=0) - valid_points.min(axis=0)
        print(f"   Bounding box size: [{bbox_size[0]:.4f}, {bbox_size[1]:.4f}, {bbox_size[2]:.4f}]")
    
    # Analyze individual view point clouds
    print("\n📷 Individual View Analysis:")
    view_files = sorted([f for f in os.listdir(results_path) if f.startswith("view_") and f.endswith("_points.npy")])
    
    for view_file in view_files:
        view_idx = view_file.split("_")[1]
        view_points = np.load(results_path / view_file)
        
        # Reshape if needed (remove batch dimension)
        if view_points.ndim == 4 and view_points.shape[0] == 1:
            view_points = view_points[0]
        
        valid_view_mask = np.isfinite(view_points).all(axis=-1)
        valid_view_count = valid_view_mask.sum()
        total_view_count = view_points.shape[0] * view_points.shape[1]
        
        print(f"   View {view_idx}: {view_points.shape} -> {valid_view_count:,}/{total_view_count:,} valid points ({valid_view_count/total_view_count*100:.1f}%)")
    
    # Analyze camera poses
    print("\n📐 Camera Pose Analysis:")
    camera_poses = np.load(results_path / "camera_poses.npy")
    print(f"   Number of camera poses: {len(camera_poses)}")
    
    for i, pose in enumerate(camera_poses):
        # Extract rotation and translation
        rotation = pose[:3, :3]
        translation = pose[:3, 3]
        
        # Check if rotation matrix is valid (orthogonal)
        is_orthogonal = np.allclose(rotation @ rotation.T, np.eye(3), atol=1e-3)
        det = np.linalg.det(rotation)
        is_proper = np.isclose(det, 1.0, atol=1e-3)
        
        print(f"   Camera {i}: Translation [{translation[0]:.4f}, {translation[1]:.4f}, {translation[2]:.4f}]")
        print(f"              Rotation valid: {is_orthogonal and is_proper} (det={det:.4f})")
    
    # File size analysis
    print("\n💾 File Size Analysis:")
    for file in expected_files:
        file_path = results_path / file
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"   {file}: {size_mb:.2f} MB")
    
    # Quality assessment
    print("\n🎯 Quality Assessment:")
    
    # Check point density
    if len(valid_points) > 100000:
        print("   ✅ High point density (>100K points)")
    elif len(valid_points) > 50000:
        print("   ⚠️  Medium point density (50K-100K points)")
    else:
        print("   ❌ Low point density (<50K points)")
    
    # Check data validity
    valid_ratio = len(valid_points) / len(combined_points)
    if valid_ratio > 0.9:
        print("   ✅ High data validity (>90% valid points)")
    elif valid_ratio > 0.7:
        print("   ⚠️  Medium data validity (70-90% valid points)")
    else:
        print("   ❌ Low data validity (<70% valid points)")
    
    # Check reconstruction completeness
    if len(view_files) == 4:
        print("   ✅ All 4 views processed successfully")
    else:
        print(f"   ❌ Only {len(view_files)}/4 views processed")
    
    # Generate validation report
    report_path = results_path / "validation_report.txt"
    with open(report_path, 'w') as f:
        f.write("Fast3R Breast Reconstruction Validation Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("File Completeness:\n")
        f.write(f"  Expected files: {len(expected_files)}\n")
        f.write(f"  Present files: {len(expected_files) - len(missing_files)}\n")
        f.write(f"  Missing files: {missing_files}\n\n")
        
        f.write("Point Cloud Statistics:\n")
        f.write(f"  Total points: {len(combined_points):,}\n")
        f.write(f"  Valid points: {len(valid_points):,} ({valid_ratio*100:.1f}%)\n")
        if len(valid_points) > 0:
            f.write(f"  Centroid: [{centroid[0]:.4f}, {centroid[1]:.4f}, {centroid[2]:.4f}]\n")
            f.write(f"  Bounding box: [{bbox_size[0]:.4f}, {bbox_size[1]:.4f}, {bbox_size[2]:.4f}]\n")
        
        f.write(f"\nCamera Poses: {len(camera_poses)} poses estimated\n")
        f.write(f"Views Processed: {len(view_files)}/4\n")
        
        f.write(f"\nOverall Quality: ")
        if valid_ratio > 0.9 and len(valid_points) > 100000 and len(view_files) == 4:
            f.write("EXCELLENT\n")
        elif valid_ratio > 0.7 and len(valid_points) > 50000 and len(view_files) >= 3:
            f.write("GOOD\n")
        else:
            f.write("NEEDS IMPROVEMENT\n")
    
    print(f"\n📄 Validation report saved to: {report_path}")
    
    return True

if __name__ == "__main__":
    results_dir = "outputs/reconstruction_results_corrected"
    validate_reconstruction_results(results_dir)
