#!/usr/bin/env python3
"""
运行Fast3R demo的包装脚本，修复兼容性问题
"""

import os
import sys
from pathlib import Path

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# 修复torch.nn.attention兼容性问题
import torch
import torch.nn as nn

if not hasattr(torch.nn, 'attention'):
    import types
    
    # 创建SDPBackend枚举
    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1
        MATH = 2
    
    # 创建sdpa_kernel上下文管理器
    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    # 创建attention模块
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module
    print("✅ 已应用torch.nn.attention兼容性修复")

# 现在导入并运行demo
print("正在启动Fast3R Demo界面...")
print("=" * 60)

try:
    # 导入demo模块
    from fast3r.viz import demo
    
    # 运行demo的main函数
    if hasattr(demo, 'main'):
        demo.main()
    else:
        # 如果没有main函数，直接运行文件
        exec(open(str(fast3r_root / 'fast3r' / 'viz' / 'demo.py')).read())
        
except Exception as e:
    print(f"\n❌ 启动demo时出错: {e}")
    import traceback
    traceback.print_exc()
    
    print("\n建议：")
    print("1. 确保所有依赖已安装")
    print("2. 检查PyTorch版本")
    print("3. 考虑在GPU环境中运行")