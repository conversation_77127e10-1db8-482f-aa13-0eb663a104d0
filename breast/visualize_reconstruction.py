#!/usr/bin/env python3
"""
Visualization script for Fast3R breast reconstruction results
Creates interactive 3D plots and saves visualization images
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os
from pathlib import Path

def create_3d_visualization(results_dir):
    """Create 3D visualization of the reconstruction results"""
    results_path = Path(results_dir)
    
    print("🎨 Creating 3D visualizations...")
    
    # Load the combined point cloud
    combined_points = np.load(results_path / "combined_points.npy")
    print(f"   Loaded {len(combined_points):,} points")
    
    # Create output directory for visualizations
    viz_dir = results_path / "visualizations"
    viz_dir.mkdir(exist_ok=True)
    
    # 1. Overall 3D point cloud visualization
    fig = plt.figure(figsize=(15, 12))
    
    # Main 3D view
    ax1 = fig.add_subplot(221, projection='3d')
    scatter = ax1.scatter(combined_points[:, 0], combined_points[:, 1], combined_points[:, 2], 
                         c=combined_points[:, 2], cmap='viridis', s=0.1, alpha=0.6)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('3D Breast Reconstruction - Overall View')
    plt.colorbar(scatter, ax=ax1, shrink=0.5, aspect=5)
    
    # Top view (X-Y plane)
    ax2 = fig.add_subplot(222)
    ax2.scatter(combined_points[:, 0], combined_points[:, 1], 
               c=combined_points[:, 2], cmap='viridis', s=0.1, alpha=0.6)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('Top View (X-Y Plane)')
    ax2.set_aspect('equal')
    
    # Side view (X-Z plane)
    ax3 = fig.add_subplot(223)
    ax3.scatter(combined_points[:, 0], combined_points[:, 2], 
               c=combined_points[:, 1], cmap='plasma', s=0.1, alpha=0.6)
    ax3.set_xlabel('X (m)')
    ax3.set_ylabel('Z (m)')
    ax3.set_title('Side View (X-Z Plane)')
    ax3.set_aspect('equal')
    
    # Front view (Y-Z plane)
    ax4 = fig.add_subplot(224)
    ax4.scatter(combined_points[:, 1], combined_points[:, 2], 
               c=combined_points[:, 0], cmap='coolwarm', s=0.1, alpha=0.6)
    ax4.set_xlabel('Y (m)')
    ax4.set_ylabel('Z (m)')
    ax4.set_title('Front View (Y-Z Plane)')
    ax4.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig(viz_dir / "breast_reconstruction_overview.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved overview visualization: {viz_dir}/breast_reconstruction_overview.png")
    
    # 2. Individual view visualizations
    print("   Creating individual view visualizations...")
    
    fig2, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i in range(4):
        view_points = np.load(results_path / f"view_{i}_points.npy")
        
        # Reshape if needed (remove batch dimension)
        if view_points.ndim == 4 and view_points.shape[0] == 1:
            view_points = view_points[0]
        
        # Flatten to get all points
        view_flat = view_points.reshape(-1, 3)
        
        # Remove invalid points
        valid_mask = np.isfinite(view_flat).all(axis=1)
        valid_points = view_flat[valid_mask]
        
        if len(valid_points) > 0:
            # Sample points for visualization (to avoid overcrowding)
            if len(valid_points) > 10000:
                indices = np.random.choice(len(valid_points), 10000, replace=False)
                sample_points = valid_points[indices]
            else:
                sample_points = valid_points
            
            axes[i].scatter(sample_points[:, 0], sample_points[:, 1], 
                           c=sample_points[:, 2], cmap='viridis', s=1, alpha=0.7)
            axes[i].set_xlabel('X (m)')
            axes[i].set_ylabel('Y (m)')
            axes[i].set_title(f'View {i} - {len(valid_points):,} points')
            axes[i].set_aspect('equal')
        else:
            axes[i].text(0.5, 0.5, 'No valid points', ha='center', va='center', transform=axes[i].transAxes)
            axes[i].set_title(f'View {i} - No data')
    
    plt.tight_layout()
    plt.savefig(viz_dir / "individual_views.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved individual views: {viz_dir}/individual_views.png")
    
    # 3. Depth map visualization
    print("   Creating depth map visualization...")
    
    fig3, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i in range(4):
        view_points = np.load(results_path / f"view_{i}_points.npy")
        
        if view_points.ndim == 4 and view_points.shape[0] == 1:
            view_points = view_points[0]
        
        # Extract depth (Z coordinate)
        depth_map = view_points[:, :, 2]
        
        # Create depth visualization
        im = axes[i].imshow(depth_map, cmap='viridis', aspect='auto')
        axes[i].set_title(f'View {i} - Depth Map')
        axes[i].set_xlabel('Width (pixels)')
        axes[i].set_ylabel('Height (pixels)')
        plt.colorbar(im, ax=axes[i])
    
    plt.tight_layout()
    plt.savefig(viz_dir / "depth_maps.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved depth maps: {viz_dir}/depth_maps.png")
    
    # 4. Camera poses visualization
    print("   Creating camera poses visualization...")
    
    camera_poses = np.load(results_path / "camera_poses.npy")
    
    fig4 = plt.figure(figsize=(12, 10))
    ax = fig4.add_subplot(111, projection='3d')
    
    # Plot point cloud (sampled)
    if len(combined_points) > 5000:
        indices = np.random.choice(len(combined_points), 5000, replace=False)
        sample_points = combined_points[indices]
    else:
        sample_points = combined_points
    
    ax.scatter(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2], 
              c='lightblue', s=0.5, alpha=0.3, label='Point Cloud')
    
    # Plot camera positions and orientations
    colors = ['red', 'green', 'blue', 'orange']
    for i, pose in enumerate(camera_poses):
        # Camera position
        pos = pose[:3, 3]
        ax.scatter(pos[0], pos[1], pos[2], c=colors[i], s=100, marker='^', 
                  label=f'Camera {i}')
        
        # Camera orientation (show viewing direction)
        direction = pose[:3, 2] * 0.02  # Z-axis of camera (viewing direction)
        ax.quiver(pos[0], pos[1], pos[2], 
                 direction[0], direction[1], direction[2], 
                 color=colors[i], arrow_length_ratio=0.1)
    
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('Camera Poses and 3D Reconstruction')
    ax.legend()
    
    plt.savefig(viz_dir / "camera_poses.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved camera poses: {viz_dir}/camera_poses.png")
    
    # 5. Statistics visualization
    print("   Creating statistics visualization...")
    
    fig5, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Point distribution histograms
    axes[0, 0].hist(combined_points[:, 0], bins=50, alpha=0.7, color='red')
    axes[0, 0].set_title('X Coordinate Distribution')
    axes[0, 0].set_xlabel('X (m)')
    axes[0, 0].set_ylabel('Count')
    
    axes[0, 1].hist(combined_points[:, 1], bins=50, alpha=0.7, color='green')
    axes[0, 1].set_title('Y Coordinate Distribution')
    axes[0, 1].set_xlabel('Y (m)')
    axes[0, 1].set_ylabel('Count')
    
    axes[1, 0].hist(combined_points[:, 2], bins=50, alpha=0.7, color='blue')
    axes[1, 0].set_title('Z Coordinate Distribution')
    axes[1, 0].set_xlabel('Z (m)')
    axes[1, 0].set_ylabel('Count')
    
    # Point count per view
    view_counts = []
    for i in range(4):
        view_points = np.load(results_path / f"view_{i}_points.npy")
        if view_points.ndim == 4:
            view_points = view_points[0]
        valid_count = np.isfinite(view_points).all(axis=-1).sum()
        view_counts.append(valid_count)
    
    axes[1, 1].bar(range(4), view_counts, color=['red', 'green', 'blue', 'orange'])
    axes[1, 1].set_title('Valid Points per View')
    axes[1, 1].set_xlabel('View Index')
    axes[1, 1].set_ylabel('Point Count')
    axes[1, 1].set_xticks(range(4))
    
    plt.tight_layout()
    plt.savefig(viz_dir / "statistics.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved statistics: {viz_dir}/statistics.png")
    
    print(f"\n🎨 All visualizations saved to: {viz_dir}")
    print("📁 Generated files:")
    print("   - breast_reconstruction_overview.png (主要3D视图)")
    print("   - individual_views.png (各视图点云)")
    print("   - depth_maps.png (深度图)")
    print("   - camera_poses.png (相机姿态)")
    print("   - statistics.png (统计信息)")
    
    return viz_dir

if __name__ == "__main__":
    results_dir = "outputs/reconstruction_results_corrected"
    viz_dir = create_3d_visualization(results_dir)
    
    print(f"\n💡 提示：")
    print(f"   1. PLY文件可用MeshLab、CloudCompare或Blender查看")
    print(f"   2. 生成的PNG图片可直接查看")
    print(f"   3. 所有可视化文件位于: {viz_dir}")
