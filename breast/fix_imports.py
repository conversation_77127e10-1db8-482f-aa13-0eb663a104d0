"""
修复PyTorch版本兼容性问题
"""
import sys
import torch

# 如果torch.nn.attention不存在，创建一个假的模块
if not hasattr(torch.nn, 'attention'):
    import types
    
    # 创建假的SDPBackend类
    class SDPBackend:
        pass
    
    # 创建假的attention模块
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    
    # 将其添加到torch.nn
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module
    
print("已修复torch.nn.attention导入问题")