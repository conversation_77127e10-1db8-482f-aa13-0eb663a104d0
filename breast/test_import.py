import sys
from pathlib import Path

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# 修复torch.nn.attention导入问题
import torch
try:
    from torch.nn.attention import SDPBackend
    print("✅ torch.nn.attention.SDPBackend 导入成功")
except ImportError:
    import types
    attention_module = types.ModuleType('torch.nn.attention')
    class SDPBackend:
        pass
    attention_module.SDPBackend = SDPBackend
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module
    print("⚠️ 创建了torch.nn.attention兼容性修复")

# 测试导入
try:
    from fast3r.dust3r.utils.image import load_images
    print("✅ 成功导入 load_images")
except Exception as e:
    print(f"❌ 导入 load_images 失败: {e}")

try:
    from fast3r.models.fast3r import Fast3R
    print("✅ 成功导入 Fast3R")
except Exception as e:
    print(f"❌ 导入 Fast3R 失败: {e}")

try:
    from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule
    print("✅ 成功导入 MultiViewDUSt3RLitModule")
except Exception as e:
    print(f"❌ 导入 MultiViewDUSt3RLitModule 失败: {e}")

print(f"\nPyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")

# 检查图像文件
data_dir = Path(__file__).parent / "data" / "Breast_fusion"
image_files = list(data_dir.glob("*.jpg"))
print(f"\n在 {data_dir} 找到 {len(image_files)} 张图像:")
for img in image_files:
    print(f"  - {img.name}")