#!/usr/bin/env python3
"""
Corrected Fast3R breast 3D reconstruction script
Following official API patterns from the README and demo.py
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from datetime import datetime

# Add Fast3R to Python path
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# Fix torch.nn.attention compatibility issue
if not hasattr(torch.nn, 'attention'):
    import types

    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1
        MATH = 2

    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False

    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module

# Import Fast3R modules
from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R
from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule

def setup_environment():
    """Setup environment and check compatibility"""
    print("🔧 Setting up environment...")
    
    # Force CPU usage to avoid CUDA issues
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    # Check PyTorch version
    print(f"   └── PyTorch version: {torch.__version__}")
    
    # Set device
    device = torch.device("cpu")
    print(f"   └── Using device: {device}")
    
    return device

def load_breast_images(data_dir, verbose=True):
    """Load and preprocess breast images"""
    if verbose:
        print("🖼️  Loading breast images...")
    
    # Get image files
    image_files = sorted(list(data_dir.glob("*.jpg")))
    if verbose:
        print(f"   └── Found {len(image_files)} images:")
        for img in image_files:
            print(f"       - {img.name}")
    
    # Convert to string paths for load_images function
    filelist = [str(f) for f in image_files]
    
    # Load images using Fast3R's official function
    # This handles proper preprocessing, resizing, and tensor conversion
    images = load_images(filelist, size=512, verbose=verbose)

    # Add required keys that the model expects
    for i, img_dict in enumerate(images):
        img_dict['dataset'] = 'breast_custom'  # Add dataset identifier
        img_dict['label'] = f'breast_view_{i}'  # Add label for the view
        # instance is already set by load_images

    if verbose:
        print(f"   └── Loaded {len(images)} images successfully")
        for i, img_dict in enumerate(images):
            print(f"       - Image {i}: shape {img_dict['img'].shape}, true_shape {img_dict['true_shape']}")

    return images, filelist

def run_reconstruction(images, model, device, verbose=True):
    """Run Fast3R 3D reconstruction"""
    if verbose:
        print("🚀 Running 3D reconstruction...")
    
    # Run inference using the official inference function
    # This properly handles batching, tensor operations, and model forward pass
    with torch.no_grad():
        output_dict, profiling_info = inference(
            images,
            model,
            device,
            dtype=torch.float32,  # Use float32 for CPU compatibility
            verbose=verbose,
            profiling=False  # Disable profiling to avoid CUDA sync issues
        )
    
    if verbose:
        print("   └── 3D reconstruction completed")
        print(f"   └── Output contains {len(output_dict['preds'])} predictions")
    
    return output_dict, profiling_info

def estimate_camera_poses(preds, verbose=True):
    """Estimate camera poses using PnP algorithm"""
    if verbose:
        print("📷 Estimating camera poses...")
    
    try:
        # Use the official camera pose estimation method
        poses_c2w_batch, estimated_focals = MultiViewDUSt3RLitModule.estimate_camera_poses(
            preds,
            niter_PnP=100,
            focal_length_estimation_method='first_view_from_global_head'
        )
        
        # Extract camera poses
        camera_poses = poses_c2w_batch[0] if poses_c2w_batch else None
        
        if verbose and camera_poses is not None:
            print(f"   └── Estimated poses for {len(camera_poses)} views")
            print(f"   └── Estimated focal lengths: {estimated_focals}")
        
        return camera_poses, estimated_focals
        
    except Exception as e:
        if verbose:
            print(f"   └── Camera pose estimation failed: {e}")
        return None, None

def extract_point_clouds(preds, verbose=True):
    """Extract 3D point clouds from predictions"""
    if verbose:
        print("☁️  Extracting 3D point clouds...")
    
    all_points = []
    view_info = []
    
    for view_idx, pred in enumerate(preds):
        try:
            # Extract point cloud from prediction
            if 'pts3d_in_other_view' in pred:
                pts3d = pred['pts3d_in_other_view'].cpu().numpy()
            elif 'pts3d' in pred:
                pts3d = pred['pts3d'].cpu().numpy()
            else:
                if verbose:
                    print(f"   └── Warning: No 3D points found in view {view_idx}")
                continue
            
            if verbose:
                print(f"   └── View {view_idx}: point cloud shape {pts3d.shape}")
            
            # Store view information
            view_info.append({
                'view_idx': view_idx,
                'shape': pts3d.shape,
                'points': pts3d
            })
            
            # Flatten points for combined point cloud
            if pts3d.ndim > 2:
                pts3d_flat = pts3d.reshape(-1, 3)
                # Filter out invalid points (NaN, inf)
                valid_mask = np.isfinite(pts3d_flat).all(axis=1)
                pts3d_flat = pts3d_flat[valid_mask]
                all_points.append(pts3d_flat)
            
        except Exception as e:
            if verbose:
                print(f"   └── Error processing view {view_idx}: {e}")
    
    # Combine all point clouds
    combined_points = None
    if all_points:
        combined_points = np.concatenate(all_points, axis=0)
        if verbose:
            print(f"   └── Combined point cloud: {len(combined_points)} points")
    
    return view_info, combined_points

def save_results(output_dir, view_info, combined_points, camera_poses, estimated_focals, filelist):
    """Save reconstruction results"""
    print("💾 Saving results...")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save summary information
    summary_file = output_dir / "reconstruction_summary.txt"
    with open(summary_file, 'w') as f:
        f.write("Fast3R Breast 3D Reconstruction Results\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Input images: {len(filelist)}\n")
        for i, img_path in enumerate(filelist):
            f.write(f"  {i+1}. {Path(img_path).name}\n")
        f.write(f"\nModel: Fast3R_ViT_Large_512\n")
        f.write(f"Device: CPU\n")
        f.write(f"Views processed: {len(view_info)}\n")
        
        if combined_points is not None:
            f.write(f"Total 3D points: {len(combined_points)}\n")
        
        if camera_poses is not None:
            f.write(f"Camera poses estimated: {len(camera_poses)}\n")
            f.write(f"Estimated focal lengths: {estimated_focals}\n")
        
        f.write("\nView Details:\n")
        for view in view_info:
            f.write(f"  View {view['view_idx']}: shape {view['shape']}\n")
    
    # Save individual view point clouds
    for view in view_info:
        view_file = output_dir / f"view_{view['view_idx']}_points.npy"
        np.save(view_file, view['points'])
    
    # Save combined point cloud
    if combined_points is not None:
        # Save as numpy array
        np.save(output_dir / "combined_points.npy", combined_points)
        
        # Save as PLY format for visualization
        ply_file = output_dir / "breast_reconstruction.ply"
        save_point_cloud_ply(combined_points, ply_file)
        
        # Save as simple XYZ format
        xyz_file = output_dir / "breast_reconstruction.xyz"
        with open(xyz_file, 'w') as f:
            for pt in combined_points:
                f.write(f"{pt[0]:.6f} {pt[1]:.6f} {pt[2]:.6f}\n")
    
    # Save camera poses
    if camera_poses is not None:
        poses_file = output_dir / "camera_poses.npy"
        np.save(poses_file, camera_poses)
        
        # Save poses in readable format
        poses_txt = output_dir / "camera_poses.txt"
        with open(poses_txt, 'w') as f:
            f.write("Camera Poses (Camera-to-World Transformation Matrices)\n")
            f.write("=" * 50 + "\n\n")
            for i, pose in enumerate(camera_poses):
                f.write(f"View {i}:\n")
                for row in pose:
                    f.write("  " + " ".join(f"{val:8.4f}" for val in row) + "\n")
                f.write("\n")
    
    print(f"   └── Results saved to: {output_dir}")
    return summary_file

def save_point_cloud_ply(points, filename):
    """Save point cloud in PLY format"""
    with open(filename, 'w') as f:
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(points)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        f.write("end_header\n")
        for pt in points:
            f.write(f"{pt[0]:.6f} {pt[1]:.6f} {pt[2]:.6f}\n")

def main():
    """Main reconstruction pipeline"""
    print("⚡ Fast3R Breast 3D Reconstruction Pipeline")
    print("=" * 50)
    
    # Setup paths
    breast_dir = Path(__file__).parent
    data_dir = breast_dir / "data" / "Breast_fusion"
    output_dir = breast_dir / "outputs" / "reconstruction_results_corrected"
    
    try:
        # 1. Setup environment
        device = setup_environment()
        
        # 2. Load model
        print("\n📥 Loading Fast3R model...")
        model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
        model = model.to(device)
        model.eval()
        print("   └── Model loaded successfully")
        
        # 3. Create lightning module for pose estimation
        print("\n🔧 Creating lightning module...")
        lit_module = MultiViewDUSt3RLitModule.load_for_inference(model)
        lit_module.eval()
        print("   └── Lightning module ready")
        
        # 4. Load images
        images, filelist = load_breast_images(data_dir)
        
        # 5. Run reconstruction
        output_dict, profiling_info = run_reconstruction(images, model, device)
        
        # 6. Extract point clouds
        view_info, combined_points = extract_point_clouds(output_dict['preds'])
        
        # 7. Estimate camera poses
        camera_poses, estimated_focals = estimate_camera_poses(output_dict['preds'])
        
        # 8. Save results
        summary_file = save_results(output_dir, view_info, combined_points, camera_poses, estimated_focals, filelist)
        
        # 9. Print final summary
        print("\n✅ Reconstruction completed successfully!")
        print(f"📄 Summary saved to: {summary_file}")
        if combined_points is not None:
            print(f"☁️  3D point cloud: {len(combined_points)} points")
        if camera_poses is not None:
            print(f"📷 Camera poses: {len(camera_poses)} views")
        
    except Exception as e:
        print(f"\n❌ Error during reconstruction: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error log
        error_file = output_dir / "error_log.txt"
        output_dir.mkdir(parents=True, exist_ok=True)
        with open(error_file, 'w') as f:
            f.write(f"Error: {str(e)}\n\n")
            traceback.print_exc(file=f)
        print(f"💾 Error log saved to: {error_file}")

if __name__ == "__main__":
    main()
