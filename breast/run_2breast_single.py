#!/usr/bin/env python3
"""
Fast3R Single Image Processing for 2Breast.jpg
Attempts to process a single breast image using Fast3R model
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from datetime import datetime
import types

# Add Fast3R to Python path
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# Fix torch.nn.attention compatibility issue
if not hasattr(torch.nn, 'attention'):
    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1 
        MATH = 2
    
    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module

# Import Fast3R modules
from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R

def setup_environment():
    """Setup PyTorch environment"""
    print("🔧 Setting up environment...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   └── PyTorch version: {torch.__version__}")
    print(f"   └── Using device: {device}")
    return device

def load_fast3r_model(device):
    """Load Fast3R model"""
    print("📥 Loading Fast3R model...")
    try:
        model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512").to(device)
        model.eval()
        print("   └── Model loaded successfully")
        return model
    except Exception as e:
        print(f"   └── Error loading model: {e}")
        raise

def load_single_image(image_path, verbose=True):
    """Load and preprocess single image"""
    if verbose:
        print(f"🖼️  Loading image: {image_path}")
    
    # Check if file exists
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")
    
    # Load single image using Fast3R's load_images function
    images = load_images([image_path], size=512, verbose=verbose)
    
    # Add required keys for Fast3R model
    for i, img_dict in enumerate(images):
        img_dict['dataset'] = '2breast_single'
        img_dict['label'] = f'2breast_view_{i}'
    
    if verbose:
        print(f"   └── Loaded image successfully")
        print(f"       - Shape: {images[0]['img'].shape}")
        print(f"       - True shape: {images[0]['true_shape']}")
    
    return images

def process_single_image(images, model, device, verbose=True):
    """Process single image with Fast3R"""
    if verbose:
        print("🚀 Processing single image...")
    
    try:
        # For single image, we can try to duplicate it to create pseudo multi-view
        # or process it as-is and see what happens
        
        # Option 1: Process single image directly
        with torch.no_grad():
            result = inference(
                images,
                model,
                device,
                dtype=torch.float32,
                verbose=verbose,
                profiling=False
            )
            
            # Handle different return formats
            if isinstance(result, tuple) and len(result) == 2:
                output_dict, profiling_info = result
            elif isinstance(result, dict):
                output_dict = result
                profiling_info = None
            else:
                output_dict = result
                profiling_info = None
        
        if verbose:
            print(f"   └── Processing completed")
            if 'preds' in output_dict:
                print(f"   └── Generated {len(output_dict['preds'])} predictions")
        
        return output_dict, profiling_info
        
    except Exception as e:
        print(f"   └── Error during processing: {e}")
        # Try alternative approach: duplicate image to create pseudo multi-view
        if verbose:
            print("   └── Trying pseudo multi-view approach...")
        
        try:
            # Create pseudo multi-view by duplicating the image
            pseudo_images = []
            for i in range(2):  # Create 2 copies
                img_copy = images[0].copy()
                img_copy['dataset'] = '2breast_pseudo'
                img_copy['label'] = f'2breast_pseudo_{i}'
                img_copy['instance'] = str(i)
                pseudo_images.append(img_copy)
            
            with torch.no_grad():
                result = inference(
                    pseudo_images,
                    model,
                    device,
                    dtype=torch.float32,
                    verbose=verbose,
                    profiling=False
                )
                
                if isinstance(result, tuple) and len(result) == 2:
                    output_dict, profiling_info = result
                elif isinstance(result, dict):
                    output_dict = result
                    profiling_info = None
                else:
                    output_dict = result
                    profiling_info = None
            
            if verbose:
                print(f"   └── Pseudo multi-view processing completed")
            
            return output_dict, profiling_info
            
        except Exception as e2:
            print(f"   └── Pseudo multi-view also failed: {e2}")
            raise e2

def extract_results(output_dict, verbose=True):
    """Extract point clouds and other results"""
    if verbose:
        print("☁️  Extracting results...")
    
    results = {
        'point_clouds': [],
        'depth_maps': [],
        'predictions': []
    }
    
    if 'preds' not in output_dict:
        print("   └── No predictions found in output")
        return results
    
    preds = output_dict['preds']
    
    for i, pred in enumerate(preds):
        try:
            # Extract point cloud
            if 'pts3d_in_other_view' in pred:
                pts3d_tensor = pred['pts3d_in_other_view']
                if pts3d_tensor.dtype == torch.bfloat16:
                    pts3d_tensor = pts3d_tensor.float()
                pts3d = pts3d_tensor.cpu().numpy()
                results['point_clouds'].append(pts3d)
                
            elif 'pts3d' in pred:
                pts3d_tensor = pred['pts3d']
                if pts3d_tensor.dtype == torch.bfloat16:
                    pts3d_tensor = pts3d_tensor.float()
                pts3d = pts3d_tensor.cpu().numpy()
                results['point_clouds'].append(pts3d)
            
            # Extract depth map if available
            if 'depth' in pred:
                depth_tensor = pred['depth']
                if depth_tensor.dtype == torch.bfloat16:
                    depth_tensor = depth_tensor.float()
                depth = depth_tensor.cpu().numpy()
                results['depth_maps'].append(depth)
            
            results['predictions'].append(pred)
            
            if verbose:
                if len(results['point_clouds']) > i:
                    pc_shape = results['point_clouds'][i].shape
                    print(f"   └── View {i}: point cloud shape {pc_shape}")
                
        except Exception as e:
            print(f"   └── Error extracting from view {i}: {e}")
            continue
    
    return results

def save_single_results(results, image_path, output_dir):
    """Save results for single image processing"""
    print("💾 Saving results...")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save summary
    summary_file = output_path / "2breast_processing_summary.txt"
    with open(summary_file, 'w') as f:
        f.write("Fast3R Single Image Processing Results - 2Breast.jpg\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Input image: {Path(image_path).name}\n")
        f.write(f"Model: Fast3R_ViT_Large_512\n")
        f.write(f"Processing mode: Single image\n\n")
        
        f.write(f"Results:\n")
        f.write(f"  Point clouds generated: {len(results['point_clouds'])}\n")
        f.write(f"  Depth maps generated: {len(results['depth_maps'])}\n")
        f.write(f"  Predictions: {len(results['predictions'])}\n\n")
        
        for i, pc in enumerate(results['point_clouds']):
            if pc.ndim >= 3:
                valid_points = np.isfinite(pc).all(axis=-1).sum()
                total_points = pc.shape[0] * pc.shape[1] if pc.ndim >= 2 else len(pc)
                f.write(f"  Point cloud {i}: shape {pc.shape}, {valid_points}/{total_points} valid points\n")
    
    # Save point clouds
    for i, pc in enumerate(results['point_clouds']):
        pc_file = output_path / f"2breast_pointcloud_{i}.npy"
        np.save(pc_file, pc)
        
        # Save as PLY if we have valid 3D points
        if pc.ndim >= 3:
            # Flatten and get valid points
            pc_flat = pc.reshape(-1, 3) if pc.ndim > 2 else pc
            valid_mask = np.isfinite(pc_flat).all(axis=1)
            valid_points = pc_flat[valid_mask]
            
            if len(valid_points) > 0:
                ply_file = output_path / f"2breast_pointcloud_{i}.ply"
                save_ply(valid_points, ply_file)
    
    # Save depth maps
    for i, depth in enumerate(results['depth_maps']):
        depth_file = output_path / f"2breast_depth_{i}.npy"
        np.save(depth_file, depth)
    
    print(f"   └── Results saved to: {output_path}")
    return output_path

def save_ply(points, filename):
    """Save point cloud in PLY format"""
    with open(filename, 'w') as f:
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(points)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        f.write("end_header\n")
        for point in points:
            f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\n")

def main():
    """Main processing function"""
    print("⚡ Fast3R Single Image Processing - 2Breast.jpg")
    print("=" * 60)
    
    try:
        # Setup
        device = setup_environment()
        model = load_fast3r_model(device)
        
        # Load image
        image_path = "data/Breast_fusion/2Breast.jpg"
        images = load_single_image(image_path, verbose=True)
        
        # Process
        output_dict, profiling_info = process_single_image(images, model, device, verbose=True)
        
        # Extract results
        results = extract_results(output_dict, verbose=True)
        
        # Save results
        output_dir = "breast/outputs/2breast_single_results"
        save_single_results(results, image_path, output_dir)
        
        print("\n✅ Single image processing completed successfully!")
        print(f"📄 Results saved to: {output_dir}")
        
        if results['point_clouds']:
            total_points = sum(np.isfinite(pc).all(axis=-1).sum() for pc in results['point_clouds'])
            print(f"☁️  Generated {total_points:,} 3D points")
        
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
