#!/usr/bin/env python3
"""
最终版breast 3D重建脚本
"""

import os
import sys
from pathlib import Path

# 设置环境
os.environ['CUDA_VISIBLE_DEVICES'] = ''

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# 修复torch.nn.attention
import torch
if not hasattr(torch.nn, 'attention'):
    import types
    
    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1 
        MATH = 2
    
    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module

import numpy as np
from fast3r.dust3r.utils.image import load_images
from fast3r.models.fast3r import Fast3R

def prepare_batch(images):
    """准备批次数据，添加必要的键"""
    batch = []
    for i, img_dict in enumerate(images):
        view = {
            'img': img_dict['img'],
            'true_shape': img_dict['true_shape'],
            'idx': i,
            'instance': str(i),
            'dataset': 'custom',  # 添加dataset键
        }
        batch.append(view)
    return batch

def main():
    breast_dir = Path(__file__).parent
    data_dir = breast_dir / "data" / "Breast_fusion"
    output_dir = breast_dir / "outputs" / "reconstruction_results"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取图像
    image_files = sorted(list(data_dir.glob("*.jpg")))
    print(f"找到 {len(image_files)} 张图像:")
    for img in image_files:
        print(f"  - {img.name}")
    
    try:
        # 设置设备
        device = torch.device("cpu")
        
        # 加载模型
        print("\n加载Fast3R模型...")
        model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
        model = model.to(device)
        model.eval()
        
        # 加载图像
        print("\n加载图像...")
        images = load_images([str(f) for f in image_files], size=512, verbose=True)
        
        # 准备批次
        batch = prepare_batch(images)
        
        # 运行模型
        print("\n运行3D重建...")
        with torch.no_grad():
            # 直接调用模型
            output = model(batch)
        
        print("\n处理输出...")
        
        # 从输出中提取预测
        if isinstance(output, tuple):
            preds = output[0]  # 如果输出是元组，取第一个元素
        else:
            preds = output
        
        # 保存简单的统计信息
        stats_file = output_dir / "reconstruction_info.txt"
        with open(stats_file, 'w') as f:
            f.write("Fast3R 3D重建信息\n")
            f.write("="*50 + "\n\n")
            f.write(f"输入图像数量: {len(image_files)}\n")
            f.write(f"图像列表:\n")
            for img in image_files:
                f.write(f"  - {img.name}\n")
            f.write(f"\n模型: Fast3R_ViT_Large_512\n")
            f.write(f"设备: {device}\n")
            
            # 检查输出类型
            if hasattr(preds, 'keys'):
                f.write(f"\n输出键: {list(preds.keys())}\n")
            else:
                f.write(f"\n输出类型: {type(preds)}\n")
        
        # 尝试提取点云数据
        if isinstance(preds, list) and len(preds) > 0:
            print(f"\n获得 {len(preds)} 个预测结果")
            
            # 收集所有点云
            all_points = []
            for i, pred in enumerate(preds):
                if isinstance(pred, dict) and 'pts3d' in pred:
                    pts3d = pred['pts3d'].cpu().numpy()
                    print(f"  视图 {i}: 点云形状 {pts3d.shape}")
                    
                    # 保存每个视图的点云
                    view_file = output_dir / f"view_{i}_points.npy"
                    np.save(view_file, pts3d)
                    
                    # 展平并添加到总点云
                    if pts3d.ndim > 2:
                        pts3d_flat = pts3d.reshape(-1, 3)
                        all_points.append(pts3d_flat)
            
            if all_points:
                # 合并所有点云
                combined_points = np.concatenate(all_points, axis=0)
                print(f"\n总点数: {len(combined_points)}")
                
                # 保存合并的点云
                np.save(output_dir / "combined_points.npy", combined_points)
                
                # 保存为简单的XYZ格式
                xyz_file = output_dir / "points.xyz"
                with open(xyz_file, 'w') as f:
                    for pt in combined_points[:10000]:  # 限制点数避免文件过大
                        f.write(f"{pt[0]:.6f} {pt[1]:.6f} {pt[2]:.6f}\n")
                
                print(f"\n点云保存到: {xyz_file}")
        
        print(f"\n结果保存在: {output_dir}")
        
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 保存错误信息
        error_file = output_dir / "error_log.txt"
        with open(error_file, 'w') as f:
            f.write(f"错误: {str(e)}\n\n")
            traceback.print_exc(file=f)

if __name__ == "__main__":
    main()