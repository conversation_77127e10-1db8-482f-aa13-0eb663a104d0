#!/usr/bin/env python3
"""
简化版breast 3D重建 - 避免CUDA相关问题
"""

import os
import sys
from pathlib import Path

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# 完整修复torch.nn.attention兼容性问题
import torch
import torch.nn as nn

# 设置为CPU模式，避免CUDA检查
os.environ['CUDA_VISIBLE_DEVICES'] = ''

if not hasattr(torch.nn, 'attention'):
    import types
    
    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1
        MATH = 2
    
    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module

import numpy as np
from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R
from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule

def main():
    # 设置路径
    breast_dir = Path(__file__).parent
    data_dir = breast_dir / "data" / "Breast_fusion"
    output_dir = breast_dir / "outputs" / "reconstruction_results"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取图像
    image_files = sorted(list(data_dir.glob("*.jpg")))
    print(f"找到 {len(image_files)} 张图像")
    
    filelist = [str(img) for img in image_files]
    
    try:
        # 强制使用CPU
        device = torch.device("cpu")
        print(f"使用设备: {device}")
        
        # 加载模型
        print("加载模型...")
        model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
        model = model.to(device)
        model.eval()
        
        # 创建lightning模块
        lit_module = MultiViewDUSt3RLitModule.load_for_inference(model)
        lit_module.eval()
        
        # 加载图像
        print("加载图像...")
        images = load_images(filelist, size=512, verbose=True)
        
        # 运行推理 - 不使用profiling避免CUDA同步
        print("运行3D重建...")
        with torch.no_grad():
            output_dict, _ = inference(
                images,
                model,
                device,
                dtype=torch.float32,
                verbose=True,
                profiling=False  # 关闭profiling
            )
        
        # 估计相机位姿
        print("估计相机位姿...")
        poses_c2w_batch, estimated_focals = MultiViewDUSt3RLitModule.estimate_camera_poses(
            output_dict['preds'],
            niter_PnP=100,
            focal_length_estimation_method='first_view_from_global_head'
        )
        
        camera_poses = poses_c2w_batch[0]
        
        # 保存相机位姿
        poses_file = output_dir / "camera_poses.txt"
        with open(poses_file, 'w') as f:
            f.write("相机位姿估计结果\n")
            f.write("="*50 + "\n\n")
            for i, pose in enumerate(camera_poses):
                f.write(f"图像 {i+1} ({image_files[i].name}):\n")
                f.write(f"位置: x={pose[0,3]:.3f}, y={pose[1,3]:.3f}, z={pose[2,3]:.3f}\n")
                f.write(f"焦距: {estimated_focals[i]:.2f}\n")
                np.savetxt(f, pose, fmt='%.4f')
                f.write("\n")
        
        # 提取点云
        print("提取点云...")
        all_points = []
        all_colors = []
        
        for view_idx, (pred, img_info) in enumerate(zip(output_dict['preds'], images)):
            pts3d = pred['pts3d_in_other_view'].cpu().numpy()[0]
            confidence = pred['conf'].cpu().numpy()[0]
            
            img = img_info['img'].cpu().numpy()
            if img.shape[0] == 3:
                img = img.transpose(1, 2, 0)
            img = (img + 1) / 2
            
            conf_threshold = np.percentile(confidence, 10)
            valid_mask = confidence > conf_threshold
            
            pts3d_flat = pts3d.reshape(-1, 3)
            img_flat = img.reshape(-1, 3)
            valid_mask_flat = valid_mask.flatten()
            
            valid_points = pts3d_flat[valid_mask_flat]
            valid_colors = img_flat[valid_mask_flat]
            
            # 转换到世界坐标
            camera_pose = camera_poses[view_idx]
            valid_points_homo = np.concatenate([valid_points, np.ones((valid_points.shape[0], 1))], axis=1)
            valid_points_world = (camera_pose @ valid_points_homo.T).T[:, :3]
            
            all_points.append(valid_points_world)
            all_colors.append(valid_colors)
            
            print(f"  视图 {view_idx+1}: {len(valid_points_world)} 个点")
        
        # 合并点云
        combined_points = np.concatenate(all_points, axis=0)
        combined_colors = np.concatenate(all_colors, axis=0)
        
        print(f"总点数: {len(combined_points)}")
        
        # 保存点云
        np.savez(output_dir / "point_cloud.npz", 
                 points=combined_points, 
                 colors=combined_colors)
        
        # 保存XYZ格式
        xyz_file = output_dir / "point_cloud.xyz"
        with open(xyz_file, 'w') as f:
            for i in range(len(combined_points)):
                pt = combined_points[i]
                col = (combined_colors[i] * 255).astype(int)
                f.write(f"{pt[0]:.6f} {pt[1]:.6f} {pt[2]:.6f} {col[0]} {col[1]} {col[2]}\n")
        
        print(f"\n完成！结果保存在: {output_dir}")
        print(f"- 相机位姿: camera_poses.txt")
        print(f"- 点云: point_cloud.npz, point_cloud.xyz")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()