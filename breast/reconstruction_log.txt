已创建兼容性修复
Warning, cannot find cuda-compiled version of RoPE2D, using a slow pytorch version instead
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pl_bolts/__init__.py:11: FutureWarning: In the future `np.object` will be defined as the corresponding NumPy scalar.
  if not hasattr(numpy, tp_name):
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pl_bolts/__init__.py:11: FutureWarning: In the future `np.bool` will be defined as the corresponding NumPy scalar.
  if not hasattr(numpy, tp_name):
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pl_bolts/models/self_supervised/amdim/amdim_module.py:34: UnderReviewWarning: The feature generate_power_seq is currently marked under review. The compatibility with other Lightning projects is not guaranteed and API may change at any time. The API and functionality may change without warning in future releases. More details: https://lightning-bolts.readthedocs.io/en/latest/stability.html
  "lr_options": generate_power_seq(LEARNING_RATE_CIFAR, 11),
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pl_bolts/models/self_supervised/amdim/amdim_module.py:92: UnderReviewWarning: The feature FeatureMapContrastiveTask is currently marked under review. The compatibility with other Lightning projects is not guaranteed and API may change at any time. The API and functionality may change without warning in future releases. More details: https://lightning-bolts.readthedocs.io/en/latest/stability.html
  contrastive_task: Union[FeatureMapContrastiveTask] = FeatureMapContrastiveTask("01, 02, 11"),
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pl_bolts/losses/self_supervised_learning.py:228: UnderReviewWarning: The feature AmdimNCELoss is currently marked under review. The compatibility with other Lightning projects is not guaranteed and API may change at any time. The API and functionality may change without warning in future releases. More details: https://lightning-bolts.readthedocs.io/en/latest/stability.html
  self.nce_loss = AmdimNCELoss(tclip)
找到 4 张图像:
  - Breast1.jpg
  - Breast2.jpg
  - Breast3.jpg
  - Breast4.jpg

正在加载Fast3R模型...
使用设备: cpu
从HuggingFace下载模型...

加载图像...
>> Loading a list of 4 images
 - adding /Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/data/Breast_fusion/Breast1.jpg with resolution 190x207 --> 464x512
 - adding /Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/data/Breast_fusion/Breast2.jpg with resolution 201x208 --> 480x512
 - adding /Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/data/Breast_fusion/Breast3.jpg with resolution 217x214 --> 512x496
 - adding /Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/data/Breast_fusion/Breast4.jpg with resolution 424x426 --> 496x512
 (Found 4 images)

运行3D重建...
>> Inference with model on 4 images
/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/amp/autocast_mode.py:267: UserWarning: In CPU autocast, but the target dtype is not supported. Disabling autocast.
CPU Autocast only supports dtype of torch.bfloat16, torch.float16 currently.
  warnings.warn(error_message)

❌ 处理过程中出错: module 'torch.nn.attention' has no attribute 'sdpa_kernel'
Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/breast/run_reconstruction_fixed.py", line 84, in main
    output_dict, profiling_info = inference(
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/dust3r/inference_multiview.py", line 82, in inference
    res = loss_of_one_batch(
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/dust3r/inference_multiview.py", line 54, in loss_of_one_batch
    preds, profiling_info = model(views, profiling=profiling)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 316, in forward
    encoded_feats, positions, shapes = self._encode_images(views)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 291, in _encode_images
    feat, pos = self.encoder(img, true_shape)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/models/fast3r.py", line 555, in forward
    x = blk(x, pos)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/croco/models/blocks.py", line 237, in forward
    x = x + self.drop_path(self.attn(self.norm1(x), xpos))
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/CodeLearning/3DSeg/fast3r-main/fast3r/croco/models/blocks.py", line 172, in forward
    with torch.nn.attention.sdpa_kernel(SDPBackend.FLASH_ATTENTION):
AttributeError: module 'torch.nn.attention' has no attribute 'sdpa_kernel'
