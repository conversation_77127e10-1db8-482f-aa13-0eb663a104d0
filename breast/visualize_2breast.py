#!/usr/bin/env python3
"""
Visualization script for 2Breast.jpg single image reconstruction results
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pathlib import Path
import PIL.Image

def visualize_2breast_results():
    """Create visualizations for 2Breast single image processing"""
    
    print("🎨 Creating 2Breast.jpg reconstruction visualizations...")
    
    results_dir = Path("breast/outputs/2breast_single_results")
    
    # Load the point cloud
    point_cloud = np.load(results_dir / "2breast_pointcloud_0.npy")
    print(f"   Loaded point cloud: shape {point_cloud.shape}")
    
    # Remove batch dimension if present
    if point_cloud.ndim == 4 and point_cloud.shape[0] == 1:
        point_cloud = point_cloud[0]
    
    # Flatten to get all points
    points_flat = point_cloud.reshape(-1, 3)
    
    # Remove invalid points
    valid_mask = np.isfinite(points_flat).all(axis=1)
    valid_points = points_flat[valid_mask]
    
    print(f"   Valid points: {len(valid_points):,}/{len(points_flat):,} ({len(valid_points)/len(points_flat)*100:.1f}%)")
    
    if len(valid_points) == 0:
        print("   ❌ No valid points found!")
        return
    
    # Create visualization directory
    viz_dir = results_dir / "visualizations"
    viz_dir.mkdir(exist_ok=True)
    
    # 1. Overall 3D visualization
    fig = plt.figure(figsize=(16, 12))
    
    # Main 3D view
    ax1 = fig.add_subplot(221, projection='3d')
    
    # Sample points for visualization if too many
    if len(valid_points) > 10000:
        indices = np.random.choice(len(valid_points), 10000, replace=False)
        sample_points = valid_points[indices]
    else:
        sample_points = valid_points
    
    scatter = ax1.scatter(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2], 
                         c=sample_points[:, 2], cmap='viridis', s=1, alpha=0.7)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('2Breast.jpg - 3D Reconstruction')
    plt.colorbar(scatter, ax=ax1, shrink=0.5, aspect=5)
    
    # Top view (X-Y plane)
    ax2 = fig.add_subplot(222)
    ax2.scatter(sample_points[:, 0], sample_points[:, 1], 
               c=sample_points[:, 2], cmap='viridis', s=1, alpha=0.7)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('Top View (X-Y Plane)')
    ax2.set_aspect('equal')
    
    # Side view (X-Z plane)
    ax3 = fig.add_subplot(223)
    ax3.scatter(sample_points[:, 0], sample_points[:, 2], 
               c=sample_points[:, 1], cmap='plasma', s=1, alpha=0.7)
    ax3.set_xlabel('X (m)')
    ax3.set_ylabel('Z (m)')
    ax3.set_title('Side View (X-Z Plane)')
    ax3.set_aspect('equal')
    
    # Front view (Y-Z plane)
    ax4 = fig.add_subplot(224)
    ax4.scatter(sample_points[:, 1], sample_points[:, 2], 
               c=sample_points[:, 0], cmap='coolwarm', s=1, alpha=0.7)
    ax4.set_xlabel('Y (m)')
    ax4.set_ylabel('Z (m)')
    ax4.set_title('Front View (Y-Z Plane)')
    ax4.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig(viz_dir / "2breast_reconstruction_3d.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved 3D visualization: {viz_dir}/2breast_reconstruction_3d.png")
    
    # 2. Depth map visualization
    fig2, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Original depth map
    depth_map = point_cloud[:, :, 2]  # Z coordinate
    im1 = axes[0].imshow(depth_map, cmap='viridis', aspect='auto')
    axes[0].set_title('2Breast.jpg - Depth Map')
    axes[0].set_xlabel('Width (pixels)')
    axes[0].set_ylabel('Height (pixels)')
    plt.colorbar(im1, ax=axes[0])
    
    # Valid points mask
    valid_mask_2d = np.isfinite(depth_map)
    im2 = axes[1].imshow(valid_mask_2d, cmap='gray', aspect='auto')
    axes[1].set_title('Valid Points Mask')
    axes[1].set_xlabel('Width (pixels)')
    axes[1].set_ylabel('Height (pixels)')
    plt.colorbar(im2, ax=axes[1])
    
    plt.tight_layout()
    plt.savefig(viz_dir / "2breast_depth_analysis.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved depth analysis: {viz_dir}/2breast_depth_analysis.png")
    
    # 3. Statistics visualization
    fig3, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Coordinate distributions
    axes[0, 0].hist(valid_points[:, 0], bins=50, alpha=0.7, color='red')
    axes[0, 0].set_title('X Coordinate Distribution')
    axes[0, 0].set_xlabel('X (m)')
    axes[0, 0].set_ylabel('Count')
    
    axes[0, 1].hist(valid_points[:, 1], bins=50, alpha=0.7, color='green')
    axes[0, 1].set_title('Y Coordinate Distribution')
    axes[0, 1].set_xlabel('Y (m)')
    axes[0, 1].set_ylabel('Count')
    
    axes[1, 0].hist(valid_points[:, 2], bins=50, alpha=0.7, color='blue')
    axes[1, 0].set_title('Z Coordinate Distribution (Depth)')
    axes[1, 0].set_xlabel('Z (m)')
    axes[1, 0].set_ylabel('Count')
    
    # Point density heatmap
    # Create 2D histogram for point density
    hist, xedges, yedges = np.histogram2d(valid_points[:, 0], valid_points[:, 1], bins=50)
    im = axes[1, 1].imshow(hist.T, origin='lower', cmap='hot', aspect='auto',
                          extent=[xedges[0], xedges[-1], yedges[0], yedges[-1]])
    axes[1, 1].set_title('Point Density (Top View)')
    axes[1, 1].set_xlabel('X (m)')
    axes[1, 1].set_ylabel('Y (m)')
    plt.colorbar(im, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig(viz_dir / "2breast_statistics.png", dpi=300, bbox_inches='tight')
    print(f"   ✅ Saved statistics: {viz_dir}/2breast_statistics.png")
    
    # 4. Load and display original image alongside reconstruction
    try:
        original_img = PIL.Image.open("data/Breast_fusion/2Breast.jpg")
        
        fig4, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Original image
        axes[0].imshow(original_img)
        axes[0].set_title('Original 2Breast.jpg')
        axes[0].axis('off')
        
        # Depth map overlay
        axes[1].imshow(depth_map, cmap='viridis', aspect='auto')
        axes[1].set_title('Generated Depth Map')
        axes[1].set_xlabel('Width (pixels)')
        axes[1].set_ylabel('Height (pixels)')
        
        plt.tight_layout()
        plt.savefig(viz_dir / "2breast_comparison.png", dpi=300, bbox_inches='tight')
        print(f"   ✅ Saved comparison: {viz_dir}/2breast_comparison.png")
        
    except Exception as e:
        print(f"   ⚠️  Could not load original image: {e}")
    
    # 5. Generate summary statistics
    stats_file = viz_dir / "2breast_analysis_summary.txt"
    with open(stats_file, 'w') as f:
        f.write("2Breast.jpg Single Image Reconstruction Analysis\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Point Cloud Statistics:\n")
        f.write(f"  Total points: {len(points_flat):,}\n")
        f.write(f"  Valid points: {len(valid_points):,} ({len(valid_points)/len(points_flat)*100:.1f}%)\n")
        f.write(f"  Point cloud shape: {point_cloud.shape}\n\n")
        
        if len(valid_points) > 0:
            f.write(f"Spatial Extent:\n")
            f.write(f"  X range: [{valid_points[:, 0].min():.4f}, {valid_points[:, 0].max():.4f}] ({valid_points[:, 0].max() - valid_points[:, 0].min():.4f}m)\n")
            f.write(f"  Y range: [{valid_points[:, 1].min():.4f}, {valid_points[:, 1].max():.4f}] ({valid_points[:, 1].max() - valid_points[:, 1].min():.4f}m)\n")
            f.write(f"  Z range: [{valid_points[:, 2].min():.4f}, {valid_points[:, 2].max():.4f}] ({valid_points[:, 2].max() - valid_points[:, 2].min():.4f}m)\n\n")
            
            centroid = valid_points.mean(axis=0)
            f.write(f"Centroid: [{centroid[0]:.4f}, {centroid[1]:.4f}, {centroid[2]:.4f}]\n\n")
        
        f.write(f"Processing Quality:\n")
        if len(valid_points) > 100000:
            f.write(f"  ✅ High point density (>100K points)\n")
        elif len(valid_points) > 50000:
            f.write(f"  ⚠️  Medium point density (50K-100K points)\n")
        else:
            f.write(f"  ❌ Low point density (<50K points)\n")
        
        valid_ratio = len(valid_points) / len(points_flat)
        if valid_ratio > 0.9:
            f.write(f"  ✅ High data validity (>90% valid points)\n")
        elif valid_ratio > 0.7:
            f.write(f"  ⚠️  Medium data validity (70-90% valid points)\n")
        else:
            f.write(f"  ❌ Low data validity (<70% valid points)\n")
    
    print(f"   ✅ Saved analysis summary: {stats_file}")
    
    print(f"\n🎨 All visualizations saved to: {viz_dir}")
    print("📁 Generated files:")
    print("   - 2breast_reconstruction_3d.png (3D重建视图)")
    print("   - 2breast_depth_analysis.png (深度分析)")
    print("   - 2breast_statistics.png (统计信息)")
    print("   - 2breast_comparison.png (原图对比)")
    print("   - 2breast_analysis_summary.txt (分析摘要)")
    
    # Print key statistics
    print(f"\n📊 Key Statistics:")
    print(f"   Total points: {len(points_flat):,}")
    print(f"   Valid points: {len(valid_points):,} ({len(valid_points)/len(points_flat)*100:.1f}%)")
    if len(valid_points) > 0:
        print(f"   Spatial extent: {valid_points[:, 0].max() - valid_points[:, 0].min():.3f}m × {valid_points[:, 1].max() - valid_points[:, 1].min():.3f}m × {valid_points[:, 2].max() - valid_points[:, 2].min():.3f}m")
    
    return viz_dir

if __name__ == "__main__":
    viz_dir = visualize_2breast_results()
    print(f"\n💡 提示：")
    print(f"   1. PLY文件可用MeshLab、CloudCompare查看: breast/outputs/2breast_single_results/2breast_pointcloud_0.ply")
    print(f"   2. 可视化图片位于: {viz_dir}")
    print(f"   3. 这是单张图像的3D重建结果，质量可能不如多视图重建")
