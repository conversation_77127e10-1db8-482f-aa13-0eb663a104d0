#!/usr/bin/env python3
"""
使用Fast3R对breast图像进行3D重建
所有输出都保存在breast目录中
"""

import os
import sys
from pathlib import Path

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.append(str(fast3r_root))

import torch
import numpy as np
from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R
from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule

# 设置路径
breast_dir = Path(__file__).parent
data_dir = breast_dir / "data" / "Breast_fusion"
output_dir = breast_dir / "outputs" / "reconstruction_results"
output_dir.mkdir(parents=True, exist_ok=True)

# 获取所有图像文件
image_files = sorted(list(data_dir.glob("*.jpg")))
print(f"找到 {len(image_files)} 张图像:")
for img in image_files:
    print(f"  - {img.name}")

filelist = [str(img) for img in image_files]

# 加载模型
print("\n加载Fast3R模型...")
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
model = model.to(device)

lit_module = MultiViewDUSt3RLitModule.load_for_inference(model)
model.eval()
lit_module.eval()

# 加载图像
print("\n加载图像...")
images = load_images(filelist, size=512, verbose=True)

# 运行推理
print("\n运行3D重建...")
output_dict, profiling_info = inference(
    images,
    model,
    device,
    dtype=torch.float32,
    verbose=True,
    profiling=True,
)

# 估计相机位姿
print("\n估计相机位姿...")
poses_c2w_batch, estimated_focals = MultiViewDUSt3RLitModule.estimate_camera_poses(
    output_dict['preds'],
    niter_PnP=100,
    focal_length_estimation_method='first_view_from_global_head'
)

camera_poses = poses_c2w_batch[0]

# 保存结果
print("\n保存结果...")

# 保存相机位姿
poses_file = output_dir / "camera_poses.txt"
with open(poses_file, 'w') as f:
    for i, (pose, img_file) in enumerate(zip(camera_poses, image_files)):
        f.write(f"=== {img_file.name} ===\n")
        f.write(f"Camera position: {pose[:3, 3]}\n")
        f.write(f"Camera pose matrix:\n")
        np.savetxt(f, pose, fmt='%.6f')
        f.write("\n")

# 保存点云
all_points = []
all_colors = []

for view_idx, (pred, img_info) in enumerate(zip(output_dict['preds'], images)):
    pts3d = pred['pts3d_in_other_view'].cpu().numpy()[0]
    confidence = pred['conf'].cpu().numpy()[0]
    
    img = img_info['img'].cpu().numpy()
    if img.shape[0] == 3:
        img = img.transpose(1, 2, 0)
    img = (img + 1) / 2
    
    conf_threshold = np.percentile(confidence, 10)
    valid_mask = confidence > conf_threshold
    
    pts3d_flat = pts3d.reshape(-1, 3)
    img_flat = img.reshape(-1, 3)
    valid_mask_flat = valid_mask.flatten()
    
    valid_points = pts3d_flat[valid_mask_flat]
    valid_colors = img_flat[valid_mask_flat]
    
    # 转换到世界坐标系
    camera_pose = camera_poses[view_idx]
    valid_points_homo = np.concatenate([valid_points, np.ones((valid_points.shape[0], 1))], axis=1)
    valid_points_world = (camera_pose @ valid_points_homo.T).T[:, :3]
    
    all_points.append(valid_points_world)
    all_colors.append(valid_colors)

combined_points = np.concatenate(all_points, axis=0)
combined_colors = np.concatenate(all_colors, axis=0)

# 保存为NumPy格式
np.savez(output_dir / "point_cloud.npz", 
         points=combined_points, 
         colors=combined_colors)

# 保存为文本格式便于查看
np.savetxt(output_dir / "point_cloud.xyz", 
           np.hstack([combined_points, combined_colors * 255]), 
           fmt='%.6f %.6f %.6f %d %d %d',
           header='x y z r g b')

print(f"\n重建完成！")
print(f"结果保存在: {output_dir}")
print(f"  - 相机位姿: camera_poses.txt")
print(f"  - 点云数据: point_cloud.npz, point_cloud.xyz")
print(f"  - 总点数: {len(combined_points)}")