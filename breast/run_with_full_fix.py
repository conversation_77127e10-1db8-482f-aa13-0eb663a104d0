#!/usr/bin/env python3
"""
完整修复版的breast 3D重建脚本
"""

import os
import sys
from pathlib import Path

# 添加Fast3R到Python路径
fast3r_root = Path(__file__).parent.parent
sys.path.insert(0, str(fast3r_root))

# 完整修复torch.nn.attention兼容性问题
import torch
import torch.nn as nn

if not hasattr(torch.nn, 'attention'):
    import types
    
    # 创建SDPBackend枚举
    class SDPBackend:
        FLASH_ATTENTION = 0
        EFFICIENT_ATTENTION = 1
        MATH = 2
    
    # 创建sdpa_kernel上下文管理器
    class sdpa_kernel:
        def __init__(self, backend):
            self.backend = backend
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    # 创建attention模块
    attention_module = types.ModuleType('torch.nn.attention')
    attention_module.SDPBackend = SDPBackend
    attention_module.sdpa_kernel = sdpa_kernel
    
    torch.nn.attention = attention_module
    sys.modules['torch.nn.attention'] = attention_module
    print("✅ 已创建完整的torch.nn.attention兼容性修复")

# 现在导入Fast3R模块
import numpy as np
from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R
from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule

def main():
    # 设置路径
    breast_dir = Path(__file__).parent
    data_dir = breast_dir / "data" / "Breast_fusion"
    output_dir = breast_dir / "outputs" / "reconstruction_results"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图像文件
    image_files = sorted(list(data_dir.glob("*.jpg")))
    if not image_files:
        print(f"未找到图像文件在: {data_dir}")
        return
    
    print(f"\n📸 找到 {len(image_files)} 张图像:")
    for img in image_files:
        print(f"   ├── {img.name}")
    
    filelist = [str(img) for img in image_files]
    
    try:
        # 加载模型
        print("\n🔧 正在加载Fast3R模型...")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"   └── 使用设备: {device}")
        
        # 从HuggingFace加载模型
        print("\n📥 从HuggingFace下载模型...")
        model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
        model = model.to(device)
        print("   └── 模型加载成功")
        
        # 创建lightning模块
        lit_module = MultiViewDUSt3RLitModule.load_for_inference(model)
        
        # 设置为评估模式
        model.eval()
        lit_module.eval()
        
        # 加载图像
        print("\n🖼️  加载图像...")
        images = load_images(filelist, size=512, verbose=True)
        
        # 运行推理
        print("\n🚀 运行3D重建...")
        with torch.no_grad():
            output_dict, profiling_info = inference(
                images,
                model,
                device,
                dtype=torch.float32,
                verbose=True,
                profiling=True,
            )
        print("   └── 3D重建完成")
        
        # 估计相机位姿
        print("\n📐 估计相机位姿...")
        poses_c2w_batch, estimated_focals = MultiViewDUSt3RLitModule.estimate_camera_poses(
            output_dict['preds'],
            niter_PnP=100,
            focal_length_estimation_method='first_view_from_global_head'
        )
        
        camera_poses = poses_c2w_batch[0]
        print("   └── 相机位姿估计完成")
        
        # 保存结果
        print("\n💾 保存结果...")
        
        # 保存相机位姿
        poses_file = output_dir / "camera_poses.txt"
        with open(poses_file, 'w') as f:
            f.write("=== 相机位姿估计结果 ===\n")
            f.write("="*60 + "\n\n")
            for i, (pose, img_file) in enumerate(zip(camera_poses, image_files)):
                f.write(f"📷 图像 {i+1}: {img_file.name}\n")
                f.write(f"   位置 (x, y, z): [{pose[0,3]:.3f}, {pose[1,3]:.3f}, {pose[2,3]:.3f}]\n")
                f.write(f"   焦距: {estimated_focals[i]:.2f}\n")
                f.write(f"   相机矩阵:\n")
                for row in pose:
                    f.write(f"      [{row[0]:7.4f} {row[1]:7.4f} {row[2]:7.4f} {row[3]:7.4f}]\n")
                f.write("\n" + "-"*60 + "\n\n")
        
        # 提取并保存点云
        print("\n☁️  提取3D点云...")
        all_points = []
        all_colors = []
        
        for view_idx, (pred, img_info) in enumerate(zip(output_dict['preds'], images)):
            # 获取点云和置信度
            pts3d = pred['pts3d_in_other_view'].cpu().numpy()[0]
            confidence = pred['conf'].cpu().numpy()[0]
            
            # 获取RGB颜色
            img = img_info['img'].cpu().numpy()
            if img.shape[0] == 3:  # CHW -> HWC
                img = img.transpose(1, 2, 0)
            img = (img + 1) / 2  # [-1, 1] -> [0, 1]
            img = np.clip(img, 0, 1)  # 确保在有效范围内
            
            # 应用置信度阈值
            conf_threshold = np.percentile(confidence, 10)
            valid_mask = confidence > conf_threshold
            
            # 展平并过滤
            pts3d_flat = pts3d.reshape(-1, 3)
            img_flat = img.reshape(-1, 3)
            valid_mask_flat = valid_mask.flatten()
            
            valid_points = pts3d_flat[valid_mask_flat]
            valid_colors = img_flat[valid_mask_flat]
            
            # 转换到世界坐标系
            camera_pose = camera_poses[view_idx]
            valid_points_homo = np.concatenate([valid_points, np.ones((valid_points.shape[0], 1))], axis=1)
            valid_points_world = (camera_pose @ valid_points_homo.T).T[:, :3]
            
            all_points.append(valid_points_world)
            all_colors.append(valid_colors)
            
            print(f"   ├── 视图 {view_idx+1}: {len(valid_points_world):,} 个有效点")
        
        # 合并所有点
        combined_points = np.concatenate(all_points, axis=0)
        combined_colors = np.concatenate(all_colors, axis=0)
        
        print(f"   └── 总点数: {len(combined_points):,}")
        
        # 保存点云数据
        np.savez(output_dir / "point_cloud.npz", 
                 points=combined_points, 
                 colors=combined_colors,
                 camera_poses=camera_poses,
                 focals=estimated_focals)
        
        # 保存为文本格式
        point_cloud_txt = output_dir / "point_cloud.xyz"
        with open(point_cloud_txt, 'w') as f:
            f.write(f"# 3D点云数据 - Fast3R重建结果\n")
            f.write(f"# 格式: x y z r g b\n")
            f.write(f"# 总点数: {len(combined_points)}\n")
            for i in range(len(combined_points)):
                pt = combined_points[i]
                col = (combined_colors[i] * 255).astype(int)
                f.write(f"{pt[0]:.6f} {pt[1]:.6f} {pt[2]:.6f} {col[0]} {col[1]} {col[2]}\n")
        
        # 保存PLY格式
        ply_file = output_dir / "point_cloud.ply"
        save_ply(combined_points, combined_colors, ply_file)
        
        print(f"\n✅ 重建完成！")
        print(f"\n📂 结果保存在: {output_dir}")
        print(f"   ├── 📄 相机位姿: camera_poses.txt")
        print(f"   ├── 💾 点云数据: point_cloud.npz")
        print(f"   ├── 📊 点云文本: point_cloud.xyz")
        print(f"   └── 🎨 PLY格式: point_cloud.ply")
        
        # 保存重建统计信息
        stats_file = output_dir / "reconstruction_stats.txt"
        with open(stats_file, 'w') as f:
            f.write("Fast3R 3D重建统计信息\n")
            f.write("="*60 + "\n\n")
            f.write(f"📸 输入图像数量: {len(image_files)}\n")
            f.write(f"☁️  总点云数量: {len(combined_points):,}\n")
            f.write(f"🖥️  计算设备: {device}\n")
            f.write(f"🤖 模型: Fast3R_ViT_Large_512\n")
            f.write(f"\n📊 各视图统计:\n")
            for i, points in enumerate(all_points):
                f.write(f"   视图 {i+1} ({image_files[i].name}):\n")
                f.write(f"      - 点数: {len(points):,}\n")
                f.write(f"      - 焦距: {estimated_focals[i]:.2f}\n")
        
        print(f"\n💡 提示:")
        print(f"   • 使用 MeshLab 或 CloudCompare 查看点云")
        print(f"   • 使用 Python + Open3D 进行进一步处理")
        print(f"   • PLY文件可直接导入大多数3D软件")
        
    except Exception as e:
        print(f"\n❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

def save_ply(points, colors, filename):
    """保存点云为PLY格式"""
    import struct
    
    # 确保颜色在[0,255]范围
    colors = np.clip(colors * 255, 0, 255).astype(np.uint8)
    
    with open(filename, 'wb') as f:
        # PLY header
        f.write(b"ply\n")
        f.write(b"format binary_little_endian 1.0\n")
        f.write(b"comment Generated by Fast3R\n")
        f.write(f"element vertex {len(points)}\n".encode())
        f.write(b"property float x\n")
        f.write(b"property float y\n")
        f.write(b"property float z\n")
        f.write(b"property uchar red\n")
        f.write(b"property uchar green\n")
        f.write(b"property uchar blue\n")
        f.write(b"end_header\n")
        
        # 写入二进制数据
        for i in range(len(points)):
            f.write(struct.pack('<fffBBB',
                               points[i, 0], points[i, 1], points[i, 2],
                               colors[i, 0], colors[i, 1], colors[i, 2]))

if __name__ == "__main__":
    main()