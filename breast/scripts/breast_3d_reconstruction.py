#!/usr/bin/env python3
"""
Breast 3D Reconstruction using Fast3R
=====================================
This script processes multiple breast images to generate 3D reconstruction and camera pose estimation.
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# Add parent directory to path to import fast3r modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from fast3r.dust3r.utils.image import load_images
from fast3r.dust3r.inference_multiview import inference
from fast3r.models.fast3r import Fast3R
from fast3r.models.multiview_dust3r_module import MultiViewDUSt3RLitModule
from fast3r.viz.viser_visualizer import ViserVisualizer

def main():
    # Setup paths
    data_dir = Path(__file__).parent.parent / "data" / "Breast_fusion"
    output_dir = Path(__file__).parent.parent / "outputs" / "breast_reconstruction"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all image files
    image_files = sorted(list(data_dir.glob("*.jpg")))
    if not image_files:
        print(f"No images found in {data_dir}")
        return
    
    print(f"Found {len(image_files)} images:")
    for img in image_files:
        print(f"  - {img.name}")
    
    # Convert to string paths
    filelist = [str(img) for img in image_files]
    
    # --- Setup Model ---
    print("\nLoading Fast3R model...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Load the pre-trained model from HuggingFace
    model = Fast3R.from_pretrained("jedyang97/Fast3R_ViT_Large_512")
    model = model.to(device)
    
    # Create lightning module wrapper
    lit_module = MultiViewDUSt3RLitModule.load_for_inference(model)
    
    # Set to evaluation mode
    model.eval()
    lit_module.eval()
    
    # --- Load Images ---
    print("\nLoading images...")
    images = load_images(filelist, size=512, verbose=True)
    
    # --- Run Inference ---
    print("\nRunning Fast3R inference...")
    output_dict, profiling_info = inference(
        images,
        model,
        device,
        dtype=torch.float32,
        verbose=True,
        profiling=True,
    )
    
    # --- Estimate Camera Poses ---
    print("\nEstimating camera poses...")
    poses_c2w_batch, estimated_focals = MultiViewDUSt3RLitModule.estimate_camera_poses(
        output_dict['preds'],
        niter_PnP=100,
        focal_length_estimation_method='first_view_from_global_head'
    )
    
    # Get camera poses
    camera_poses = poses_c2w_batch[0]
    
    # Print camera poses
    print("\nCamera Poses (Camera-to-World transformations):")
    for view_idx, pose in enumerate(camera_poses):
        print(f"\nView {view_idx} ({image_files[view_idx].name}):")
        print(f"  Pose shape: {pose.shape}")
        print(f"  Camera position: {pose[:3, 3]}")
    
    # --- Extract and Save 3D Point Clouds ---
    print("\nExtracting 3D point clouds...")
    all_points = []
    all_colors = []
    
    for view_idx, (pred, img_info) in enumerate(zip(output_dict['preds'], images)):
        # Get point cloud in camera coordinates
        pts3d = pred['pts3d_in_other_view'].cpu().numpy()[0]  # Shape: (H, W, 3)
        confidence = pred['conf'].cpu().numpy()[0]  # Shape: (H, W)
        
        # Get RGB colors
        img = img_info['img'].cpu().numpy()
        if img.shape[0] == 3:  # CHW format
            img = img.transpose(1, 2, 0)  # Convert to HWC
        
        # Denormalize image from [-1, 1] to [0, 1]
        img = (img + 1) / 2
        
        # Apply confidence threshold
        conf_threshold = np.percentile(confidence, 10)  # Keep top 90% confident points
        valid_mask = confidence > conf_threshold
        
        # Flatten and filter points
        pts3d_flat = pts3d.reshape(-1, 3)
        img_flat = img.reshape(-1, 3)
        valid_mask_flat = valid_mask.flatten()
        
        valid_points = pts3d_flat[valid_mask_flat]
        valid_colors = img_flat[valid_mask_flat]
        
        # Transform points to world coordinates
        camera_pose = camera_poses[view_idx]
        # Convert points to homogeneous coordinates
        valid_points_homo = np.concatenate([valid_points, np.ones((valid_points.shape[0], 1))], axis=1)
        # Transform to world coordinates
        valid_points_world = (camera_pose @ valid_points_homo.T).T[:, :3]
        
        all_points.append(valid_points_world)
        all_colors.append(valid_colors)
        
        print(f"  View {view_idx}: {len(valid_points_world)} valid points")
    
    # Combine all points
    combined_points = np.concatenate(all_points, axis=0)
    combined_colors = np.concatenate(all_colors, axis=0)
    
    print(f"\nTotal points in combined cloud: {len(combined_points)}")
    
    # --- Save Results ---
    # Save point cloud as PLY
    save_path_ply = output_dir / "breast_reconstruction.ply"
    save_point_cloud_ply(combined_points, combined_colors, save_path_ply)
    print(f"\nSaved point cloud to: {save_path_ply}")
    
    # Save camera poses
    poses_path = output_dir / "camera_poses.npz"
    np.savez(
        poses_path,
        poses=camera_poses,
        focals=estimated_focals,
        image_names=[img.name for img in image_files]
    )
    print(f"Saved camera poses to: {poses_path}")
    
    # Save individual view information
    for view_idx in range(len(images)):
        view_dir = output_dir / f"view_{view_idx}"
        view_dir.mkdir(exist_ok=True)
        
        # Save depth map
        depth = output_dict['preds'][view_idx]['depth'].cpu().numpy()[0, 0]
        np.save(view_dir / "depth.npy", depth)
        
        # Save confidence map
        conf = output_dict['preds'][view_idx]['conf'].cpu().numpy()[0]
        np.save(view_dir / "confidence.npy", conf)
        
        # Save camera parameters
        np.savez(
            view_dir / "camera_params.npz",
            pose=camera_poses[view_idx],
            focal=estimated_focals[view_idx],
            image_name=image_files[view_idx].name
        )
    
    print(f"\nReconstruction complete! Results saved in: {output_dir}")
    
    # --- Optional: Launch Visualization ---
    print("\nWould you like to visualize the results? This will open a web interface.")
    print("Note: You can also run the visualization demo separately with:")
    print(f"  python fast3r/viz/demo.py")

def save_point_cloud_ply(points, colors, filename):
    """Save point cloud to PLY format."""
    import struct
    
    # Ensure colors are in [0, 255] range
    colors = (colors * 255).astype(np.uint8)
    
    with open(filename, 'wb') as f:
        # PLY header
        f.write(b"ply\n")
        f.write(b"format binary_little_endian 1.0\n")
        f.write(f"element vertex {len(points)}\n".encode())
        f.write(b"property float x\n")
        f.write(b"property float y\n")
        f.write(b"property float z\n")
        f.write(b"property uchar red\n")
        f.write(b"property uchar green\n")
        f.write(b"property uchar blue\n")
        f.write(b"end_header\n")
        
        # Write binary data
        for i in range(len(points)):
            f.write(struct.pack('<fffBBB',
                               points[i, 0], points[i, 1], points[i, 2],
                               colors[i, 0], colors[i, 1], colors[i, 2]))

if __name__ == "__main__":
    main()